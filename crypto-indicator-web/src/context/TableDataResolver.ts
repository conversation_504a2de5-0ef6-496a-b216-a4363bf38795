import { useMemo } from 'react';

import { useTableDataManager } from '@/hooks/useTableDataManager';

import type { TableProviderProps } from './TableTypes';
import type { AssetStatisticsDto } from '@/utils/assetTableFiltering';

interface ResolvedTableData {
  processedData: AssetStatisticsDto[];
  btcStatistics: AssetStatisticsDto[];
  totalCount: number;
  loading: boolean;
  error: string | null;
  onSignalClick: (symbol: string, currency: string) => Promise<void>;
  onRefresh: () => void;
  formatDate: (date?: string) => string;
  findBtcDataForSymbol: (
    btcStats: AssetStatisticsDto[],
    symbol: string,
  ) => any;
}

/**
 * Hook to resolve table data from props or default data manager
 * Handles the logic of whether to use passed data or fetch from API
 */
export const useResolvedData = (props: TableProviderProps): ResolvedTableData => {
  const shouldUsePassedData = props.data !== undefined;
  const defaultData = useTableDataManager({ assetType: props.assetType });

  const processedData = useMemo((): AssetStatisticsDto[] => {
    if (shouldUsePassedData) {
      return (props.data ?? []) as AssetStatisticsDto[];
    }
    return defaultData.processedData as AssetStatisticsDto[];
  }, [shouldUsePassedData, props.data, defaultData.processedData]);

  const btcStatistics = useMemo((): AssetStatisticsDto[] => {
    if (shouldUsePassedData) {
      return (props.btcStatistics ?? []) as AssetStatisticsDto[];
    }
    return defaultData.btcStatistics as AssetStatisticsDto[];
  }, [shouldUsePassedData, props.btcStatistics, defaultData.btcStatistics]);

  const totalCount: number = shouldUsePassedData ? (props.totalCount ?? 0) : defaultData.totalCount;
  const loading: boolean = shouldUsePassedData ? (props.loading ?? false) : defaultData.loading;
  const error: string | null = shouldUsePassedData ? (props.error ?? null) : defaultData.error;
  const onSignalClick = props.onSignalClick ?? defaultData.onSignalClick;
  const onRefresh = props.onRefresh ?? defaultData.onRefresh;

  return {
    processedData,
    btcStatistics,
    totalCount,
    loading,
    error,
    onSignalClick,
    onRefresh,
    formatDate: defaultData.formatDate,
    findBtcDataForSymbol: defaultData.findBtcDataForSymbol,
  };
};

import React, { createContext, useEffect, useMemo, useReducer } from 'react';

import { useTableDataManager } from '@/hooks/useTableDataManager';
import { applyAssetFilters } from '@/utils/assetTableFiltering';
import { applyAssetSorting } from '@/utils/assetTableSorting';

import type {
  TableAction,
  TableActions,
  TableContextValue,
  TableProviderProps,
  TableState,
} from './TableTypes';
import type { CryptoCurrencyStatisticsDto, IndicatorValueDto, StockStatisticsDto } from '@/generated';
import type { FilterConfig, SortColumn, SortDirection, StockFilterConfig, StockSortColumn } from '@/types/table';
import type { AssetStatisticsDto } from '@/utils/assetTableFiltering';

// Default filter configurations
const DEFAULT_FILTERS = { symbolSearch: '', usdSignal: 'all', btcSignal: 'all' } as const;
const DEFAULT_CRYPTO_FILTERS: FilterConfig = DEFAULT_FILTERS;
const DEFAULT_STOCK_FILTERS: StockFilterConfig = DEFAULT_FILTERS;

// Initial state factory
const createInitialState = (assetType: 'crypto' | 'stock'): TableState => ({
  rawData: [],
  data: [],
  btcStatistics: [],
  loading: false,
  error: null,
  assetType,
  filterConfig: assetType === 'crypto' ? DEFAULT_CRYPTO_FILTERS : DEFAULT_STOCK_FILTERS,
  hasActiveFilters: false,
  filteredCount: 0,
  totalCount: 0,
  sortConfig: {
    column: null,
    direction: null,
  },
});

// Table Reducer
// eslint-disable-next-line max-lines-per-function
const tableReducer = (state: TableState, action: TableAction): TableState => {
  switch (action.type) {
    case 'SET_DATA': {
      return {
        ...state,
        rawData: action.payload.data,
        data: action.payload.data,
        btcStatistics: action.payload.btcData,
        totalCount: action.payload.totalCount,
        filteredCount: action.payload.data.length,
        loading: false,
        error: null,
      };
    }
    
    case 'SET_LOADING': {
      return {
        ...state,
        loading: action.payload,
        ...(action.payload && { error: null }),
      };
    }
    
    case 'SET_ERROR': {
      return {
        ...state,
        error: action.payload,
        loading: false,
      };
    }
    
    case 'SET_FILTER': {
      const newFilterConfig = { ...state.filterConfig, ...action.payload };

      // Check if filters are active
      const defaultFilters = state.assetType === 'crypto' ? DEFAULT_CRYPTO_FILTERS : DEFAULT_STOCK_FILTERS;
      const hasActiveFilters = Object.keys(newFilterConfig).some(key =>
        newFilterConfig[key as keyof typeof newFilterConfig] !== defaultFilters[key as keyof typeof defaultFilters]
      );

      return {
        ...state,
        filterConfig: newFilterConfig,
        hasActiveFilters,
      };
    }
    
    case 'SET_SORT': {
      const newSortConfig = {
        column: action.payload.column,
        direction: action.payload.direction,
      };

      return {
        ...state,
        sortConfig: newSortConfig,
      };
    }
    
    case 'SET_ASSET_TYPE': {
      const newFilterConfig = action.payload === 'crypto' ? DEFAULT_CRYPTO_FILTERS : DEFAULT_STOCK_FILTERS;
      const newSortConfig = { column: null, direction: null };

      return {
        ...state,
        assetType: action.payload,
        filterConfig: newFilterConfig,
        sortConfig: newSortConfig,
        hasActiveFilters: false,
      };
    }
    
    case 'SET_FILTERED_COUNT': {
      return {
        ...state,
        filteredCount: action.payload,
      };
    }
    
    case 'SET_ACTIVE_FILTERS': {
      return {
        ...state,
        hasActiveFilters: action.payload,
      };
    }
    
    default: {
      return state;
    }
  }
};

// Note: Filtering and sorting logic is now handled by useTableDataManager

// Create Context
const TableContext = createContext<TableContextValue | null>(null);



interface TableActionsConfig {
  state: TableState;
  dispatch: React.Dispatch<TableAction>;
  onSignalClick: (symbol: string, currency: string) => Promise<void>;
  onRefresh: () => void;
  formatDate: (date?: string) => string;
  findBtcDataForSymbol: (btcStats: CryptoCurrencyStatisticsDto[] | StockStatisticsDto[], symbol: string) => IndicatorValueDto | undefined;
  assetType: 'crypto' | 'stock';
}

/**
 * Hook to create table actions
 */
const useTableActions = (config: TableActionsConfig): TableActions => {
  const { state, dispatch, onSignalClick, onRefresh, formatDate, findBtcDataForSymbol, assetType } = config;
  return useMemo<TableActions>(() => ({
    onSignalClick,
    onRefresh,

    onSort: (column: SortColumn | StockSortColumn) => {
      const currentDirection = state.sortConfig.column === column ? state.sortConfig.direction : null;
      let newDirection: SortDirection;

      if (currentDirection === null) {
        newDirection = 'desc';
      } else if (currentDirection === 'desc') {
        newDirection = 'asc';
      } else {
        newDirection = null;
      }

      dispatch({
        type: 'SET_SORT',
        payload: {
          column: newDirection === null ? null : column,
          direction: newDirection
        },
      });
    },

    getSortDirection: (column: SortColumn | StockSortColumn) => {
      return state.sortConfig.column === column ? state.sortConfig.direction : null;
    },

    onFilterChange: (filters: Partial<FilterConfig | StockFilterConfig>) => {
      dispatch({ type: 'SET_FILTER', payload: filters });
    },

    onClearFilters: () => {
      const defaultFilters = assetType === 'crypto' ? DEFAULT_CRYPTO_FILTERS : DEFAULT_STOCK_FILTERS;
      dispatch({ type: 'SET_FILTER', payload: defaultFilters });
    },

    formatDate,
    findBtcDataForSymbol,
  }), [state.sortConfig, onSignalClick, onRefresh, formatDate, findBtcDataForSymbol, assetType, dispatch]);
};

interface StateUpdatesConfig {
  dispatch: React.Dispatch<TableAction>;
  processedData: AssetStatisticsDto[];
  btcStatistics: AssetStatisticsDto[];
  totalCount: number;
  loading: boolean;
  error: string | null;
  assetType: 'crypto' | 'stock';
}

/**
 * Hook to handle state updates via useEffect
 */
const useStateUpdates = (config: StateUpdatesConfig) => {
  const { dispatch, processedData, btcStatistics, totalCount, loading, error, assetType } = config;
  // Update context state when data changes
  useEffect(() => {
    dispatch({
      type: 'SET_DATA',
      payload: {
        data: processedData as CryptoCurrencyStatisticsDto[] | StockStatisticsDto[],
        btcData: btcStatistics as CryptoCurrencyStatisticsDto[] | StockStatisticsDto[],
        totalCount,
      },
    });
  }, [dispatch, processedData, btcStatistics, totalCount]);

  useEffect(() => { dispatch({ type: 'SET_LOADING', payload: loading }); }, [dispatch, loading]);
  useEffect(() => { dispatch({ type: 'SET_ERROR', payload: error }); }, [dispatch, error]);
  useEffect(() => { dispatch({ type: 'SET_ASSET_TYPE', payload: assetType }); }, [dispatch, assetType]);
};

/**
 * Hook to resolve data from props or defaults
 */
const useResolvedData = (props: TableProviderProps) => {
  const shouldUsePassedData = props.data !== undefined;
  const defaultData = useTableDataManager({ assetType: props.assetType });

  const processedData = useMemo(() => {
    if (shouldUsePassedData) {
      return props.data ?? [];
    }
    return defaultData.processedData;
  }, [shouldUsePassedData, props.data, defaultData.processedData]);

  const btcStatistics = useMemo(() => {
    if (shouldUsePassedData) {
      return props.btcStatistics ?? [];
    }
    return defaultData.btcStatistics;
  }, [shouldUsePassedData, props.btcStatistics, defaultData.btcStatistics]);

  const totalCount = shouldUsePassedData ? (props.totalCount ?? 0) : defaultData.totalCount;
  const loading = shouldUsePassedData ? (props.loading ?? false) : defaultData.loading;
  const error = shouldUsePassedData ? (props.error ?? null) : defaultData.error;
  const onSignalClick = props.onSignalClick ?? defaultData.onSignalClick;
  const onRefresh = props.onRefresh ?? defaultData.onRefresh;

  return {
    processedData,
    btcStatistics,
    totalCount,
    loading,
    error,
    onSignalClick,
    onRefresh,
    formatDate: defaultData.formatDate,
    findBtcDataForSymbol: defaultData.findBtcDataForSymbol,
  };
};

// Table Provider Component
export const TableProvider: React.FC<TableProviderProps> = (props) => {
  const [state, dispatch] = useReducer(tableReducer, createInitialState(props.assetType));
  const resolvedData = useResolvedData(props);

  // Handle state updates
  useStateUpdates({
    dispatch,
    processedData: resolvedData.processedData,
    btcStatistics: resolvedData.btcStatistics,
    totalCount: resolvedData.totalCount,
    loading: resolvedData.loading,
    error: resolvedData.error,
    assetType: props.assetType,
  });

  // Create table actions
  const actions = useTableActions({
    state,
    dispatch,
    onSignalClick: resolvedData.onSignalClick,
    onRefresh: resolvedData.onRefresh,
    formatDate: resolvedData.formatDate,
    findBtcDataForSymbol: resolvedData.findBtcDataForSymbol,
    assetType: props.assetType,
  });

  const contextValue = useMemo<TableContextValue>(() => ({
    state,
    dispatch,
    actions,
  }), [state, actions]);

  return (
    <TableContext.Provider value={contextValue}>
      {props.children}
    </TableContext.Provider>
  );
};

export { TableContext };
export {type TableState, type TableAction, type TableActions, type TableContextValue} from './TableTypes';

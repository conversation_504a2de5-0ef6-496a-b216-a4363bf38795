import type { TableAction, TableState } from './TableTypes';

// Table reducer function
export const tableReducer = (state: TableState, action: TableAction): TableState => {
  switch (action.type) {
    case 'SET_DATA': {
      return {
        ...state,
        rawData: action.payload.data,
        btcStatistics: action.payload.btcData,
        totalCount: action.payload.totalCount,
        loading: false,
        error: null,
      };
    }
    case 'SET_LOADING': {
      return {
        ...state,
        loading: action.payload,
        ...(action.payload && { error: null }),
      };
    }
    case 'SET_ERROR': {
      return {
        ...state,
        error: action.payload,
        loading: false,
      };
    }
    case 'SET_FILTER': {
      const defaultFilters = { symbolSearch: '', usdSignal: 'all', btcSignal: 'all' } as const;
      const newFilterConfig = { ...state.filterConfig, ...action.payload };
      
      // Check if filters are active
      const hasActiveFilters = Object.keys(newFilterConfig).some(key =>
        newFilterConfig[key as keyof typeof newFilterConfig] !== defaultFilters[key as keyof typeof defaultFilters]
      );

      return {
        ...state,
        filterConfig: newFilterConfig,
        hasActiveFilters,
      };
    }
    case 'SET_SORT': {
      return {
        ...state,
        sortConfig: {
          column: action.payload.column,
          direction: action.payload.direction,
        },
      };
    }
    case 'SET_ASSET_TYPE': {
      const defaultFilters = { symbolSearch: '', usdSignal: 'all', btcSignal: 'all' } as const;
      return {
        ...state,
        assetType: action.payload,
        filterConfig: defaultFilters,
        sortConfig: { column: null, direction: null },
        hasActiveFilters: false,
      };
    }
    case 'SET_FILTERED_COUNT': {
      return {
        ...state,
        filteredCount: action.payload,
      };
    }
    case 'SET_ACTIVE_FILTERS': {
      return {
        ...state,
        hasActiveFilters: action.payload,
      };
    }
    default: {
      return state;
    }
  }
};

import { CURRENCIES, DATA_PROCESSING } from '@/constants/app';

import type {
  CryptoCurrencyStatisticsDto,
  IndicatorValueDto,
  StockIndicatorValueDto,
  StockStatisticsDto,
} from '@/generated';

export const filterByCurrency = (
  statistics: CryptoCurrencyStatisticsDto[] | StockStatisticsDto[],
  currency: string,
): (CryptoCurrencyStatisticsDto | StockStatisticsDto)[] => {
  return statistics.filter(crypto => crypto.conversionCurrency === currency);
};

export const findBtcDataForSymbol = (
  btcStatistics: CryptoCurrencyStatisticsDto[] | StockStatisticsDto[],
  symbol: string,
): IndicatorValueDto | undefined => {
  return btcStatistics
    .find(el => el.symbol === symbol)
    ?.indicatorValues?.find(el => el);
};

export const formatDate = (isoString?: string): string => {
  if (isoString === null || isoString === undefined || isoString === '') {
    return DATA_PROCESSING.DEFAULT_VALUE;
  }
  return new Date(isoString).toLocaleDateString();
};

/**
 * Get the latest indicator value for any asset type
 */
export const getLatestAssetData = (
  asset: CryptoCurrencyStatisticsDto | StockStatisticsDto,
): IndicatorValueDto | StockIndicatorValueDto | undefined => {
  if (asset.indicatorValues.length === 0) {
    return undefined;
  }
  return asset.indicatorValues.at(-1);
};

/**
 * Generic function to process asset statistics for both crypto and stock data
 * Consolidates the duplicate logic from processStockStatistics and processCryptoStatistics
 */
export const processAssetStatistics = <T extends CryptoCurrencyStatisticsDto | StockStatisticsDto>(
  statistics: T[],
  options: {
    filterEmptyIndicators?: boolean;
    usdPropertyName?: string;
    btcPropertyName?: string;
  } = {},
) => {
  const {
    filterEmptyIndicators = false,
    usdPropertyName = 'usdStatistics',
    btcPropertyName = 'btcStatistics',
  } = options;

  // Apply optional filter for empty indicators (used by stock processing)
  const filteredStatistics = filterEmptyIndicators
    ? statistics.filter((stat): stat is T => stat.indicatorValues.length > 0)
    : statistics;

  // Separate USD and BTC data properly
  const usdStatistics = filterByCurrency(
    filteredStatistics as (CryptoCurrencyStatisticsDto | StockStatisticsDto)[],
    CURRENCIES.USD
  ).sort(
    (a, b) => a.symbol.localeCompare(b.symbol),
  ) as T[];
  const btcStatistics = filterByCurrency(
    filteredStatistics as (CryptoCurrencyStatisticsDto | StockStatisticsDto)[],
    CURRENCIES.BTC
  ) as T[];

  return {
    [usdPropertyName]: usdStatistics,
    [btcPropertyName]: btcStatistics,
    totalCount: usdStatistics.length,
  };
};

/**
 * Process cryptocurrency statistics (legacy function for backward compatibility)
 */
export const processCryptoStatistics = (
  statistics: CryptoCurrencyStatisticsDto[],
) => {
  return processAssetStatistics(statistics, {
    filterEmptyIndicators: false,
    usdPropertyName: 'usdStatistics',
    btcPropertyName: 'btcStatistics',
  }) as {
    usdStatistics: CryptoCurrencyStatisticsDto[];
    btcStatistics: CryptoCurrencyStatisticsDto[];
    totalCount: number;
  };
};

/**
 * Process stock statistics (replaces the duplicate function from stockDataProcessors.ts)
 */
export const processStockStatistics = (
  statistics: StockStatisticsDto[],
) => {
  return processAssetStatistics(statistics, {
    filterEmptyIndicators: true,
    usdPropertyName: 'stockStatistics',
    btcPropertyName: 'btcStatistics',
  }) as {
    stockStatistics: StockStatisticsDto[];
    btcStatistics: StockStatisticsDto[];
    totalCount: number;
  };
};

import { useMemo } from 'react';

import type { CryptoCurrencyStatisticsDto, IndicatorValueDto, StockStatisticsDto } from '@/generated';
import { useTableDataManager } from '@/hooks/useTableDataManager';
import type { AssetStatisticsDto } from '@/utils/assetTableFiltering';

import type { TableProviderProps } from './TableTypes';

interface ResolvedTableData {
  processedData: AssetStatisticsDto[];
  btcStatistics: AssetStatisticsDto[];
  totalCount: number;
  loading: boolean;
  error: string | null;
  onSignalClick: (symbol: string, currency: string) => Promise<void>;
  onRefresh: () => void;
  formatDate: (date?: string) => string;
  findBtcDataForSymbol: (
    btcStats: CryptoCurrencyStatisticsDto[] | StockStatisticsDto[],
    symbol: string,
  ) => IndicatorValueDto | undefined;
}

/**
 * Hook to resolve table data from props or default data manager
 * Handles the logic of whether to use passed data or fetch from API
 */
export const useResolvedData = (props: TableProviderProps): ResolvedTableData => {
  const shouldUsePassedData = props.data !== undefined;
  const defaultData = useTableDataManager({ assetType: props.assetType });

  const processedData = useMemo((): AssetStatisticsDto[] => {
    if (shouldUsePassedData) {
      return props.data ?? [];
    }
    return defaultData.processedData;
  }, [shouldUsePassedData, props.data, defaultData.processedData]);

  const btcStatistics = useMemo((): AssetStatisticsDto[] => {
    if (shouldUsePassedData) {
      return props.btcStatistics ?? [];
    }
    return defaultData.btcStatistics;
  }, [shouldUsePassedData, props.btcStatistics, defaultData.btcStatistics]);

  const totalCount = shouldUsePassedData ? (props.totalCount ?? 0) : defaultData.totalCount;
  const loading = shouldUsePassedData ? (props.loading ?? false) : defaultData.loading;
  const error = shouldUsePassedData ? (props.error ?? null) : defaultData.error;
  const onSignalClick = props.onSignalClick ?? defaultData.onSignalClick;
  const onRefresh = props.onRefresh ?? defaultData.onRefresh;

  return {
    processedData,
    btcStatistics,
    totalCount,
    loading,
    error,
    onSignalClick,
    onRefresh,
    formatDate: defaultData.formatDate,
    findBtcDataForSymbol: defaultData.findBtcDataForSymbol,
  };
};

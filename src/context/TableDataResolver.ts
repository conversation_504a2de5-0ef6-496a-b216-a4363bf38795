import { useMemo } from 'react';

import type { CryptoCurrencyStatisticsDto, IndicatorValueDto, StockStatisticsDto } from '@/generated';
import { useTableDataManager } from '@/hooks/useTableDataManager';
import type { AssetStatisticsDto } from '@/utils/assetTableFiltering';

import type { TableProviderProps } from './TableTypes';

interface ResolvedTableData {
  processedData: AssetStatisticsDto[];
  btcStatistics: AssetStatisticsDto[];
  totalCount: number;
  loading: boolean;
  error: string | null;
  onSignalClick: (symbol: string, currency: string) => Promise<void>;
  onRefresh: () => void;
  formatDate: (date?: string) => string;
  findBtcDataForSymbol: (
    btcStats: CryptoCurrencyStatisticsDto[] | StockStatisticsDto[],
    symbol: string,
  ) => IndicatorValueDto | undefined;
}

/**
 * Hook to resolve table data from props or default data manager
 * Handles the logic of whether to use passed data or fetch from API
 */
export const useResolvedData = (props: TableProviderProps): ResolvedTableData => {
  const shouldUsePassedData = props.data !== undefined;
  const defaultData = useTableDataManager({ assetType: props.assetType });

  const processedData = useMemo((): AssetStatisticsDto[] => {
    if (shouldUsePassedData) {
      return props.data ?? [];
    }
    return defaultData.processedData;
  }, [shouldUsePassedData, props.data, defaultData.processedData]);

  const btcStatistics = useMemo((): AssetStatisticsDto[] => {
    if (shouldUsePassedData) {
      return props.btcStatistics ?? [];
    }
    return defaultData.btcStatistics;
  }, [shouldUsePassedData, props.btcStatistics, defaultData.btcStatistics]);

  const totalCount: number = shouldUsePassedData ? (props.totalCount ?? 0) : (defaultData.totalCount as number);
  const loading: boolean = shouldUsePassedData ? (props.loading ?? false) : (defaultData.loading as boolean);
  const error: string | null = shouldUsePassedData ? (props.error ?? null) : (defaultData.error as string | null);
  const onSignalClick: (symbol: string, currency: string) => Promise<void> = props.onSignalClick ?? (defaultData.onSignalClick as (symbol: string, currency: string) => Promise<void>);
  const onRefresh: () => void = props.onRefresh ?? (defaultData.onRefresh as () => void);

  return {
    processedData,
    btcStatistics,
    totalCount,
    loading,
    error,
    onSignalClick,
    onRefresh,
    formatDate: defaultData.formatDate as (date?: string) => string,
    findBtcDataForSymbol: defaultData.findBtcDataForSymbol as (
      btcStats: CryptoCurrencyStatisticsDto[] | StockStatisticsDto[],
      symbol: string,
    ) => IndicatorValueDto | undefined,
  };
};

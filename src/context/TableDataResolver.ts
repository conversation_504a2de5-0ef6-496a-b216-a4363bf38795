import { useMemo } from 'react';

import type { CryptoCurrencyStatisticsDto, IndicatorValueDto, StockStatisticsDto } from '@/generated';
import { useTableDataManager } from '@/hooks/useTableDataManager';
import type { AssetStatisticsDto } from '@/utils/assetTableFiltering';

import type { TableProviderProps } from './TableTypes';

interface ResolvedTableData {
  processedData: AssetStatisticsDto[];
  btcStatistics: AssetStatisticsDto[];
  totalCount: number;
  loading: boolean;
  error: string | null;
  onSignalClick: (symbol: string, currency: string) => Promise<void>;
  onRefresh: () => void;
  formatDate: (date?: string) => string;
  findBtcDataForSymbol: (
    btcStats: CryptoCurrencyStatisticsDto[] | StockStatisticsDto[],
    symbol: string,
  ) => IndicatorValueDto | undefined;
}

/**
 * Hook to resolve table data from props or default data manager
 * Handles the logic of whether to use passed data or fetch from API
 */
export const useResolvedData = (props: TableProviderProps): ResolvedTableData => {
  const shouldUsePassedData = props.data !== undefined;
  const defaultData = useTableDataManager({ assetType: props.assetType });

  const processedData = useMemo(() => {
    if (shouldUsePassedData) {
      return props.data ?? [];
    }
    return defaultData.processedData;
  }, [shouldUsePassedData, props.data, defaultData.processedData]);

  const btcStatistics = useMemo(() => {
    if (shouldUsePassedData) {
      return props.btcStatistics ?? [];
    }
    return defaultData.btcStatistics;
  }, [shouldUsePassedData, props.btcStatistics, defaultData.btcStatistics]);

  // Explicitly type the resolved values
  const resolvedData: ResolvedTableData = {
    processedData,
    btcStatistics,
    totalCount: shouldUsePassedData ? (props.totalCount ?? 0) : defaultData.totalCount,
    loading: shouldUsePassedData ? (props.loading ?? false) : defaultData.loading,
    error: shouldUsePassedData ? (props.error ?? null) : defaultData.error,
    onSignalClick: props.onSignalClick ?? defaultData.onSignalClick,
    onRefresh: props.onRefresh ?? defaultData.onRefresh,
    formatDate: defaultData.formatDate,
    findBtcDataForSymbol: defaultData.findBtcDataForSymbol,
  };

  return resolvedData;
};

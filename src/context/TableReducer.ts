import type { TableAction, TableState } from './TableTypes';

// Helper functions to reduce reducer complexity
const handleSetData = (state: TableState, action: TableAction): TableState => ({
  ...state,
  rawData: action.payload.data,
  btcStatistics: action.payload.btcData,
  totalCount: action.payload.totalCount,
  loading: false,
  error: null,
});

const handleSetFilter = (state: TableState, action: TableAction): TableState => {
  const defaultFilters = { symbolSearch: '', usdSignal: 'all', btcSignal: 'all' } as const;
  const newFilterConfig = { ...state.filterConfig, ...action.payload };

  // Check if filters are active
  const hasActiveFilters = Object.keys(newFilterConfig).some(key =>
    newFilterConfig[key as keyof typeof newFilterConfig] !== defaultFilters[key as keyof typeof defaultFilters]
  );

  return {
    ...state,
    filterConfig: newFilterConfig,
    hasActiveFilters,
  };
};

const handleSetAssetType = (state: TableState, action: TableAction): TableState => {
  const defaultFilters = { symbolSearch: '', usdSignal: 'all', btcSignal: 'all' } as const;
  return {
    ...state,
    assetType: action.payload,
    filterConfig: defaultFilters,
    sortConfig: { column: null, direction: null },
    hasActiveFilters: false,
  };
};

const handleSetLoading = (state: TableState, action: TableAction): TableState => ({
  ...state,
  loading: action.payload,
  ...(action.payload && { error: null }),
});

const handleSetError = (state: TableState, action: TableAction): TableState => ({
  ...state,
  error: action.payload,
  loading: false,
});

const handleSetSort = (state: TableState, action: TableAction): TableState => ({
  ...state,
  sortConfig: {
    column: action.payload.column,
    direction: action.payload.direction,
  },
});

const handleSetFilteredCount = (state: TableState, action: TableAction): TableState => ({
  ...state,
  filteredCount: action.payload,
});

const handleSetActiveFilters = (state: TableState, action: TableAction): TableState => ({
  ...state,
  hasActiveFilters: action.payload,
});

// Table reducer function
export const tableReducer = (state: TableState, action: TableAction): TableState => {
  switch (action.type) {
    case 'SET_DATA': return handleSetData(state, action);
    case 'SET_LOADING': return handleSetLoading(state, action);
    case 'SET_ERROR': return handleSetError(state, action);
    case 'SET_FILTER': return handleSetFilter(state, action);
    case 'SET_SORT': return handleSetSort(state, action);
    case 'SET_ASSET_TYPE': return handleSetAssetType(state, action);
    case 'SET_FILTERED_COUNT': return handleSetFilteredCount(state, action);
    case 'SET_ACTIVE_FILTERS': return handleSetActiveFilters(state, action);
    default: return state;
  }
};

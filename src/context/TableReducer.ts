import type { TableAction, TableState } from './TableTypes';

// Helper functions to reduce reducer complexity
const handleSetData = (state: TableState, action: TableAction): TableState => ({
  ...state,
  rawData: action.payload.data,
  btcStatistics: action.payload.btcData,
  totalCount: action.payload.totalCount,
  loading: false,
  error: null,
});

const checkActiveFilters = (newFilterConfig: Record<string, unknown>): boolean => {
  const defaultFilters = { symbolSearch: '', usdSignal: 'all', btcSignal: 'all' } as const;
  return Object.keys(newFilterConfig).some(key =>
    newFilterConfig[key as keyof typeof newFilterConfig] !== defaultFilters[key as keyof typeof defaultFilters]
  );
};

const handleSetFilter = (state: TableState, action: TableAction): TableState => {
  const newFilterConfig = { ...state.filterConfig, ...action.payload };
  const hasActiveFilters = checkActiveFilters(newFilterConfig);

  return {
    ...state,
    filterConfig: newFilterConfig,
    hasActiveFilters,
  };
};

const handleSetAssetType = (state: TableState, action: TableAction): TableState => {
  const defaultFilters = { symbolSearch: '', usdSignal: 'all', btcSignal: 'all' } as const;
  return {
    ...state,
    assetType: action.payload,
    filterConfig: defaultFilters,
    sortConfig: { column: null, direction: null },
    hasActiveFilters: false,
  };
};

const handleSetLoading = (state: TableState, action: TableAction): TableState => ({
  ...state,
  loading: action.payload,
  ...(action.payload && { error: null }),
});

const handleSetError = (state: TableState, action: TableAction): TableState => ({
  ...state,
  error: action.payload,
  loading: false,
});

const handleSetSort = (state: TableState, action: TableAction): TableState => ({
  ...state,
  sortConfig: {
    column: action.payload.column,
    direction: action.payload.direction,
  },
});

const handleSetFilteredCount = (state: TableState, action: TableAction): TableState => ({
  ...state,
  filteredCount: action.payload,
});

const handleSetActiveFilters = (state: TableState, action: TableAction): TableState => ({
  ...state,
  hasActiveFilters: action.payload,
});

// Action handlers map for cleaner reducer
const actionHandlers = {
  SET_DATA: handleSetData,
  SET_LOADING: handleSetLoading,
  SET_ERROR: handleSetError,
  SET_FILTER: handleSetFilter,
  SET_SORT: handleSetSort,
  SET_ASSET_TYPE: handleSetAssetType,
  SET_FILTERED_COUNT: handleSetFilteredCount,
  SET_ACTIVE_FILTERS: handleSetActiveFilters,
} as const;

// Table reducer function
export const tableReducer = (state: TableState, action: TableAction): TableState => {
  const handler = actionHandlers[action.type as keyof typeof actionHandlers];
  return handler ? handler(state, action) : state;
};
